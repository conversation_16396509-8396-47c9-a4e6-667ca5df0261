using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Account.Web.Common.Translate.Models;
using CommonLib;
using Newtonsoft.Json;

namespace Account.Web.Common.Translate.Engines
{
    /// <summary>
    /// Microsoft Edge 免费翻译引擎（最高优先级）
    /// </summary>
    public class EdgeTranslationEngine : TranslationEngineBase
    {
        private readonly string _translateUrl = "https://api.cognitive.microsofttranslator.com/translate?from={from}&to={to}&api-version=3.0&includeSentenceLength=true";

        // 认证相关字段（实例级别）
        private readonly SemaphoreSlim _authSemaphore = new SemaphoreSlim(1, 1);
        private string _currentToken;
        private DateTime _tokenExpiry;
        private readonly object _tokenLock = new object();

        // 认证配置
        private readonly string _authUrl = "https://edge.microsoft.com/translate/auth";
        private readonly TimeSpan _tokenValidityDuration = TimeSpan.FromMinutes(8); // 提前2分钟刷新
        private readonly TimeSpan _authTimeout = TimeSpan.FromSeconds(30);

        public override string Name => "Edge";
        public override int Priority => 0; // 最高优先级

        public EdgeTranslationEngine()
        {
        }

        /// <summary>
        /// 获取有效的认证令牌
        /// 使用双重检查锁定模式确保线程安全
        /// </summary>
        /// <returns>有效的认证令牌，如果获取失败返回null</returns>
        private async Task<string> GetValidTokenAsync()
        {
            // 第一次检查：如果当前令牌有效，直接返回
            if (!string.IsNullOrEmpty(_currentToken) && DateTime.UtcNow < _tokenExpiry)
                return _currentToken;

            // 获取信号量，确保只有一个线程执行认证
            var result = await _authSemaphore.WaitAsync(_authTimeout);
            try
            {
                // 第二次检查：防止在等待期间其他线程已经更新了令牌
                if (!string.IsNullOrEmpty(_currentToken) && DateTime.UtcNow < _tokenExpiry)
                    return _currentToken;

                if (result)
                {
                    var tokenValue = WebClientSyncExt.GetHtml(_authUrl, "", "", "https://www.bing.com/translator");
                    if (!string.IsNullOrEmpty(tokenValue) && tokenValue.Length > 10 &&
                        !tokenValue.Contains("error") && !tokenValue.Contains("Error"))
                    {
                        lock (_tokenLock)
                        {
                            _currentToken = tokenValue;
                            _tokenExpiry = string.IsNullOrEmpty(_currentToken) ? DateTime.MinValue : DateTime.UtcNow.Add(_tokenValidityDuration);
                        }
                    }
                }

                return _currentToken;
            }
            finally
            {
                if (result)
                    _authSemaphore.Release();
            }
        }

        /// <summary>
        /// 清除当前token（用于认证失败时重试）
        /// </summary>
        private void ClearToken()
        {
            lock (_tokenLock)
            {
                _currentToken = null;
                _tokenExpiry = DateTime.MinValue;
            }
        }

        /// <summary>
        /// 检测是否为认证错误响应
        /// </summary>
        /// <param name="response">API响应内容</param>
        /// <returns>是否为认证错误</returns>
        private bool IsAuthenticationError(string response)
        {
            if (string.IsNullOrEmpty(response))
                return false;

            try
            {
                if (response.TrimStart().StartsWith("{\"error\""))
                {
                    var errorResponse = JsonConvert.DeserializeObject<dynamic>(response);
                    var errorCode = errorResponse?.error?.code?.ToString();
                    return errorCode == "401" || errorCode == "401001" || errorCode == "401002";
                }
            }
            catch
            {
                // 解析失败，不认为是认证错误
            }

            return false;
        }

        public override async Task<TranslationResponse> TranslateAsync(TranslationRequest request)
        {
            if (!ValidateRequest(request))
            {
                return CreateErrorResponse("请求参数无效");
            }

            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 获取有效的认证令牌
                var authToken = await GetValidTokenAsync();

                if (string.IsNullOrEmpty(authToken))
                {
                    IsAvailable = false;
                    return CreateErrorResponse("Edge翻译服务当前不可用，认证失败");
                }

                // 使用语言映射服务转换语言代码
                var fromLang = LanguageMapper.ConvertToEngineLanguageCode(request.SourceLanguage, "Edge");
                var toLang = LanguageMapper.ConvertToEngineLanguageCode(request.TargetLanguage, "Edge");

                var translatedTexts = new List<string>();

                // 分批处理，Edge API建议每次不超过50个文本
                const int batchSize = 50;
                var batches = request.Texts.Select((text, index) => new { text, index })
                                          .GroupBy(x => x.index / batchSize)
                                          .Select(g => g.Select(x => x.text).ToList())
                                          .ToList();

                foreach (var batch in batches)
                {
                    var batchResult = await TranslateBatchWithRetryAsync(batch, fromLang, toLang, authToken);
                    translatedTexts.AddRange(batchResult);
                }

                stopwatch.Stop();

                IsAvailable = true;
                return CreateSuccessResponse(translatedTexts.ToArray(), stopwatch.Elapsed);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var errorMessage = "Edge翻译异常: " + ex.Message;
                LogHelper.Log.Error(errorMessage, ex);
                IsAvailable = false;
                return CreateErrorResponse(errorMessage);
            }
        }

        /// <summary>
        /// 批量翻译（带认证失败重试）
        /// </summary>
        private async Task<List<string>> TranslateBatchWithRetryAsync(List<string> texts, string fromLang, string toLang, string token)
        {
            try
            {
                // 第一次尝试翻译
                return await TranslateBatchAsync(texts, fromLang, toLang, token);
            }
            catch (Exception ex)
            {
                // 检查是否为认证错误
                if (ex.Message.StartsWith("AUTHENTICATION_ERROR:"))
                {
                    LogHelper.Log.Info("检测到认证错误，清除token并重试");
                    ClearToken();

                    // 重新获取token并重试一次
                    var newToken = await GetValidTokenAsync();
                    if (!string.IsNullOrEmpty(newToken))
                    {
                        return await TranslateBatchAsync(texts, fromLang, toLang, newToken);
                    }
                }

                // 重新抛出异常
                throw;
            }
        }

        /// <summary>
        /// 批量翻译
        /// </summary>
        private async Task<List<string>> TranslateBatchAsync(List<string> texts, string fromLang, string toLang, string token)
        {
            var requestBody = texts.Select(text => new { Text = text }).ToArray();
            var requestJson = JsonConvert.SerializeObject(requestBody);

            var url = _translateUrl.Replace("{from}", fromLang).Replace("{to}", toLang);
            var header = new NameValueCollection() {
                { "Authorization", "Bearer " + token}
            };

            var html = WebClientSyncExt.GetHtml(url, "", requestJson, "https://www.bing.com/translator", 10, header);

            // 检查响应是否为错误格式
            if (string.IsNullOrEmpty(html))
            {
                throw new Exception("Edge API返回空响应");
            }

            // 检测认证错误并抛出特殊异常
            if (IsAuthenticationError(html))
            {
                throw new Exception($"AUTHENTICATION_ERROR: {html}");
            }

            // 尝试检测其他错误响应
            if (html.TrimStart().StartsWith("{\"error\""))
            {
                // 尝试解析错误信息
                try
                {
                    var errorResponse = JsonConvert.DeserializeObject<dynamic>(html);
                    var errorMessage = errorResponse?.error?.message?.ToString() ?? "未知错误";
                    throw new Exception($"Edge API错误: {errorMessage}");
                }
                catch (JsonException)
                {
                    throw new Exception($"Edge API返回错误响应: {html}");
                }
            }

            // 尝试解析正常的翻译结果
            try
            {
                var translationResults = JsonConvert.DeserializeObject<EdgeTranslationResult[]>(html);

                if (translationResults == null || translationResults.Length == 0)
                {
                    throw new Exception("Edge API返回空的翻译结果");
                }

                return translationResults
                    .Select(result => result.Translations?.FirstOrDefault()?.Text ?? "")
                    .ToList();
            }
            catch (JsonException ex)
            {
                throw new Exception($"Edge API响应格式错误: {ex.Message}. 响应内容: {html}");
            }
        }

        public override async Task<bool> HealthCheckAsync()
        {
            return true;
        }

        public void Dispose()
        {
            _authSemaphore?.Dispose();
        }
    }

    #region Edge API 响应模型

    public class EdgeTranslationResult
    {
        public EdgeTranslation[] Translations { get; set; }
    }

    public class EdgeTranslation
    {
        public string Text { get; set; }
        public string To { get; set; }
    }

    #endregion
}
